use actix_web::{web, HttpRequest, HttpResponse};
use serde_json::json;
use tracing::{info, warn, error};

use crate::services::{AuthService, EmailVerificationService};
use crate::models::{CreateUserRequest, LoginRequest};
use crate::models::email_verification::SendVerificationEmailRequest;
use crate::utils::errors::AppError;
use crate::utils::{
    network::{extract_client_ip_from_request, sanitize_ip_for_logging},
    validation::validate_email,
    crypto::mask_sensitive_data,
};

pub fn configure_auth_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/login", web::post().to(login))
       .route("/register", web::post().to(register))
       .route("/logout", web::post().to(logout))
       .route("/verify", web::post().to(verify_token));
}

/// User registration endpoint
/// POST /auth/register
///
/// # Arguments
/// * `auth_service` - Authentication service instance
/// * `email_verification_service` - Email verification service instance
/// * `req` - HTTP request for IP extraction
/// * `req_body` - JSON payload containing user registration details
///
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response with registration status or error details
///
/// # Errors
/// * `AppError::BadRequest` - If the registration data is invalid
/// * `AppError::Conflict` - If the user already exists
/// * `AppError::InternalServerError` - If the registration process fails
pub async fn register(
    crabshield: web::Data<AuthService>,
    email_verification_service: web::Data<EmailVerificationService>,
    req: HttpRequest,
    req_body: web::Json<CreateUserRequest>,
) -> Result<HttpResponse, AppError> {
    // Extract client IP for security logging
    let client_ip = extract_client_ip_from_request(&req).unwrap_or_else(|| "unknown".to_string());
    let sanitized_ip = sanitize_ip_for_logging(&client_ip);

    // Validate email format before processing
    if let Err(e) = validate_email(&req_body.email) {
        warn!("Registration attempt with invalid email format from IP: {}", sanitized_ip);
        return Err(AppError::BadRequest(format!("Invalid email format: {}", e)));
    }

    info!("User registration request for email: {} from IP: {}",
          mask_sensitive_data(&req_body.email, 3), sanitized_ip);

    // Extract request data before any await points to avoid holding the request across await
    let user_data = req_body.into_inner();
    let email = user_data.email.clone();
    
    match crabshield.create_user(user_data).await { // Using crabshield instead of auth_service
        Ok(user) => {
            info!("User created successfully: {}", user.id);

            // Send verification email
            let verification_request = SendVerificationEmailRequest {
                email: user.email.clone(),
                resend: Some(false),
            };

            match email_verification_service.send_verification_email(verification_request).await {
                Ok(_) => {
                    info!("Verification email sent to: {}", email);
                    Ok(HttpResponse::Created().json(json!({
                        "success": true,
                        "message": "User registered successfully. Please check your email to verify your account.",
                        "user_id": user.id,
                        "email_verification_sent": true
                    })))
                }
                Err(e) => {
                    warn!("Failed to send verification email: {}", e);
                    Ok(HttpResponse::Created().json(json!({
                        "success": true,
                        "message": "User registered successfully, but verification email could not be sent. Please request a new verification email.",
                        "user_id": user.id,
                        "email_verification_sent": false
                    })))
                }
            }
        }
        Err(e) => {
            error!("User registration failed: {}", e);
            match e {
        AppError::BadRequest(msg) => Err(AppError::BadRequest(msg)),
        AppError::Conflict(msg) => Err(AppError::Conflict(msg)),
        _ => Err(AppError::InternalServerError("Registration failed".to_string()))
    }
        }
    }
}

/// User login endpoint
/// POST /auth/login
///
/// # Arguments
/// * `auth_service` - Authentication service instance
/// * `http_req` - HTTP request for IP extraction and security context
/// * `req_body` - JSON payload containing login credentials
///
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response with authentication tokens or error details
///
/// # Errors
/// * `AppError::BadRequest` - If the login data is invalid
/// * `AppError::Unauthorized` - If the credentials are invalid or MFA is required
/// * `AppError::InternalServerError` - If the authentication process fails
pub async fn login(
    auth_service: web::Data<AuthService>,
    http_req: HttpRequest,
    req_body: web::Json<LoginRequest>,
) -> Result<HttpResponse, AppError> {
    // Extract client IP for security logging and rate limiting
    let client_ip = extract_client_ip_from_request(&http_req).unwrap_or_else(|| "unknown".to_string());
    let sanitized_ip = sanitize_ip_for_logging(&client_ip);

    // Validate email format before processing
    if let Err(e) = validate_email(&req_body.email) {
        warn!("Login attempt with invalid email format from IP: {}", sanitized_ip);
        return Err(AppError::BadRequest(format!("Invalid email format: {}", e)));
    }

    info!("Login attempt for email: {} from IP: {}",
          mask_sensitive_data(&req_body.email, 3), sanitized_ip);
    let login_req = req_body.into_inner();
    let mfa_code = login_req.mfa_code.clone();
    let remember_me = login_req.remember_me.unwrap_or(false);

    // Authenticate the user with real IP address
    let auth_response = auth_service.authenticate_user(login_req, &client_ip).await?;

    // Check if MFA is required
    if auth_response.mfa_required {
        // If MFA is enabled but no code was provided
        if mfa_code.is_none() {
            return Ok(HttpResponse::Ok().json(json!({
                "message": "MFA required",
                "requires_mfa": true,
                "mfa_methods": ["totp"],
                "mfa_token": auth_response.mfa_token
            })));
        }

        // If we get here, MFA code was provided and needs to be verified
        let mfa_code = mfa_code.as_ref()
            .ok_or_else(|| AppError::BadRequest("MFA code required".to_string()))?;

        // Complete MFA verification and get tokens
        let tokens = auth_service.complete_mfa_verification(
            auth_response.user.id,
            crate::models::MfaVerifyRequest {
                code: mfa_code.clone(),
                verification_type: "TOTP".to_string(),
                trust_device: Some(false),
            },
            extract_client_ip_from_request(&http_req).and_then(|ip| ip.parse().ok()),
            http_req.headers().get("user-agent").and_then(|h| h.to_str().ok()).map(String::from),
            http_req.headers().get("x-device-fingerprint").and_then(|h| h.to_str().ok()).map(String::from),
            remember_me,
        ).await?;

        info!("MFA verification successful for user: {}", auth_response.user.id);

        return Ok(HttpResponse::Ok().json(json!({
            "message": "Login successful",
            "access_token": tokens.access_token,
            "refresh_token": tokens.refresh_token,
            "expires_in": tokens.expires_in,
            "token_type": "Bearer",
            "user": {
                "id": auth_response.user.id,
                "email": auth_response.user.email,
                "first_name": auth_response.user.first_name,
                "last_name": auth_response.user.last_name,
                "email_verified": auth_response.user.email_verified_at.is_some(),
                "mfa_enabled": auth_response.user.mfa_enabled
            }
        })));
    }

    // If no MFA required, we should have tokens
    let tokens = auth_response.tokens
        .ok_or_else(|| AppError::InternalServerError("Expected tokens but none provided".to_string()))?;

    let user = auth_response.user;

    // Return success response with tokens
    Ok(HttpResponse::Ok().json(json!({
        "message": "Login successful",
        "access_token": tokens.access_token,
        "refresh_token": tokens.refresh_token,
        "expires_in": tokens.expires_in,
        "token_type": "Bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email_verified": user.email_verified_at.is_some(),
            "mfa_enabled": user.mfa_enabled
        }
    })))
}

/// User logout endpoint
/// POST /auth/logout
///
/// # Arguments
/// * `auth_service` - Authentication service instance
/// * `user` - Authenticated user from JWT token
/// * `req` - HTTP request for IP extraction
///
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response confirming logout
///
/// # Errors
/// * `AppError::InternalServerError` - If the logout process fails
pub async fn logout(
    _auth_service: web::Data<AuthService>,
    user: crate::middleware::auth::AuthenticatedUser,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let client_ip = extract_client_ip_from_request(&req).unwrap_or_else(|| "unknown".to_string());
    let sanitized_ip = sanitize_ip_for_logging(&client_ip);

    info!("Logout request for user: {} from IP: {}", user.user_id, sanitized_ip);

    // TODO: Implement session invalidation
    // For now, we'll just log the logout event
    // In a complete implementation, you would:
    // 1. Add the JWT token to a blacklist/revocation list
    // 2. Clear any server-side session data
    // 3. Optionally notify other services of the logout

    info!("User {} logged out successfully from IP: {}", user.user_id, sanitized_ip);

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Logged out successfully"
    })))
}

/// Token verification endpoint
/// POST /auth/verify
///
/// # Arguments
/// * `user` - Authenticated user from JWT token (validates the token automatically)
///
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response with token verification status
///
/// # Errors
/// * `AppError::Unauthorized` - If the token is invalid (handled by middleware)
/// * `AppError::InternalServerError` - If the verification process fails
pub async fn verify_token(
    user: crate::middleware::auth::AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    // If we reach this point, the token has already been validated by the auth middleware
    info!("Token verification successful for user: {}", user.user_id);

    Ok(HttpResponse::Ok().json(json!({
        "valid": true,
        "message": "Token is valid",
        "user": {
            "id": user.user_id,
            "email": mask_sensitive_data(&user.email, 3),
            "mfa_verified": user.mfa_verified,
            "trusted_device": user.trusted_device
        }
    })))
}
