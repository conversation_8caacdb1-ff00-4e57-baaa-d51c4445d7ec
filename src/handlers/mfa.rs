use actix_web::{web, HttpRequest, HttpResponse, Result};
use serde_json::json;
use uuid::Uuid;
use std::net::IpAddr;
use validator::Validate;
use tracing::{info, warn};

use crate::services::MfaService;
use crate::models::{
    MfaSetupRequest, MfaVerifySetupRequest, MfaVerifyRequest,
};
use crate::utils::{validation::validate_totp_code, errors::AppError, network::extract_client_ip_addr};
use crate::utils::errors::AppError;
use crate::middleware::auth::AuthenticatedUser;

/// Initialize MFA setup for authenticated user
/// POST /api/v1/mfa/setup
pub async fn setup_mfa(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
    req_body: web::Json<MfaSetupRequest>,
) -> Result<HttpResponse, AppError> {
    let setup_response = mfa_service
        .setup_mfa(user.user_id, req_body.into_inner())
        .await
        .map_err(|e| AppError::InternalServerError(e.to_string()))?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": setup_response,
        "message": "MFA setup initiated. Please verify with your authenticator app."
    })))
}

/// Complete MFA setup by verifying TOTP code
/// POST /api/v1/mfa/verify-setup
pub async fn verify_setup(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
    req_body: web::Json<MfaVerifySetupRequest>,
) -> Result<HttpResponse, AppError> {
    mfa_service
        .verify_setup(user.user_id, req_body.into_inner())
        .await
        .map_err(|e| match e {
            crate::services::mfa_service::MfaError::InvalidTotpCode => {
                AppError::BadRequest("Invalid TOTP code".to_string())
            },
            crate::services::mfa_service::MfaError::MfaAlreadyEnabled => {
                AppError::BadRequest("MFA is already enabled".to_string())
            },
            _ => AppError::InternalServerError(e.to_string()),
        })?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "MFA has been successfully enabled for your account."
    })))
}

/// Verify MFA code during login (used by auth handlers)
/// POST /api/v1/mfa/verify
pub async fn verify_mfa(
    mfa_service: web::Data<MfaService>,
    req: HttpRequest,
    req_body: web::Json<MfaVerifyRequest>,
) -> Result<HttpResponse, AppError> {
    // Extract user_id from request (this would typically come from a temporary login token)
    let user_id = extract_user_id_from_request(&req)?;
    
    // Extract client information
    let ip_address = extract_client_ip(&req);
    let user_agent = req.headers()
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());
    let device_fingerprint = req.headers()
        .get("x-device-fingerprint")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    let verify_response = mfa_service
        .verify_mfa(
            user_id,
            req_body.into_inner(),
            ip_address,
            user_agent,
            device_fingerprint,
        )
        .await
        .map_err(|e| match e {
            crate::services::mfa_service::MfaError::InvalidTotpCode |
            crate::services::mfa_service::MfaError::InvalidBackupCode => {
                AppError::Unauthorized("Invalid MFA code".to_string())
            },
            crate::services::mfa_service::MfaError::MfaNotEnabled => {
                AppError::BadRequest("MFA is not enabled for this account".to_string())
            },
            _ => AppError::InternalServerError(e.to_string()),
        })?;

    if verify_response.success {
        Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": verify_response,
            "message": "MFA verification successful."
        })))
    } else {
        Ok(HttpResponse::Unauthorized().json(json!({
            "success": false,
            "message": "MFA verification failed."
        })))
    }
}

/// Generate new backup codes
/// POST /api/v1/mfa/backup-codes
pub async fn generate_backup_codes(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let backup_codes_response = mfa_service
        .generate_new_backup_codes(user.user_id)
        .await
        .map_err(|e| match e {
            crate::services::mfa_service::MfaError::MfaNotEnabled => {
                AppError::BadRequest("MFA is not enabled for this account".to_string())
            },
            _ => AppError::InternalServerError(e.to_string()),
        })?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": backup_codes_response,
        "message": "New backup codes generated. Please store them securely."
    })))
}

/// Get MFA status for authenticated user
/// GET /api/v1/mfa/status
pub async fn get_mfa_status(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let status = mfa_service
        .get_mfa_status(user.user_id)
        .await
        .map_err(|e| AppError::InternalServerError(e.to_string()))?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": status
    })))
}

/// Disable MFA for authenticated user
/// DELETE /api/v1/mfa/disable
pub async fn disable_mfa(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    mfa_service
        .disable_mfa(user.user_id)
        .await
        .map_err(|e| AppError::InternalServerError(e.to_string()))?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "MFA has been disabled for your account."
    })))
}

/// Get list of trusted devices
/// GET /api/v1/mfa/trusted-devices
pub async fn get_trusted_devices(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let devices = mfa_service
        .get_trusted_devices(user.user_id)
        .await
        .map_err(|e| AppError::InternalServerError(e.to_string()))?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": devices
    })))
}

/// Remove a trusted device
/// DELETE /api/v1/mfa/trusted-devices/{device_id}
pub async fn revoke_trusted_device(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
    path: web::Path<Uuid>,
) -> Result<HttpResponse, AppError> {
    let device_id = path.into_inner();
    
    mfa_service
        .revoke_trusted_device(user.user_id, device_id)
        .await
        .map_err(|e| match e {
            crate::services::mfa_service::MfaError::DeviceNotTrusted => {
                AppError::NotFound("Trusted device not found".to_string())
            },
            _ => AppError::InternalServerError(e.to_string()),
        })?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Trusted device has been revoked."
    })))
}

/// Check if device is trusted (utility endpoint)
/// GET /api/v1/mfa/device-trust-status
pub async fn check_device_trust(
    mfa_service: web::Data<MfaService>,
    user: AuthenticatedUser,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let device_fingerprint = req.headers()
        .get("x-device-fingerprint")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| AppError::BadRequest("Device fingerprint header required".to_string()))?;

    let is_trusted = mfa_service
        .is_device_trusted(user.user_id, device_fingerprint)
        .await
        .map_err(|e| AppError::InternalServerError(e.to_string()))?;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": {
            "is_trusted": is_trusted,
            "device_fingerprint": device_fingerprint
        }
    })))
}

// Helper functions

fn extract_user_id_from_request(req: &HttpRequest) -> Result<Uuid, AppError> {
    // This would typically extract user_id from a temporary login token
    // For now, we'll extract it from a header (this should be implemented properly)
    req.headers()
        .get("x-temp-user-id")
        .and_then(|h| h.to_str().ok())
        .and_then(|s| Uuid::parse_str(s).ok())
        .ok_or_else(|| AppError::BadRequest("User ID not found in request".to_string()))
}

fn extract_client_ip(req: &HttpRequest) -> Option<IpAddr> {
    // Try to get IP from X-Forwarded-For header first (for proxy setups)
    if let Some(forwarded_for) = req.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            // Take the first IP from the comma-separated list
            if let Some(first_ip) = forwarded_str.split(',').next() {
                if let Ok(ip) = first_ip.trim().parse::<IpAddr>() {
                    return Some(ip);
                }
            }
        }
    }

    // Try X-Real-IP header
    if let Some(real_ip) = req.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return Some(ip);
            }
        }
    }

    // Fall back to connection info
    req.connection_info().peer_addr()
        .and_then(|addr| addr.parse::<IpAddr>().ok())
}

/// Configure MFA routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/mfa")
            .route("/setup", web::post().to(setup_mfa))
            .route("/verify-setup", web::post().to(verify_setup))
            .route("/verify", web::post().to(verify_mfa))
            .route("/backup-codes", web::post().to(generate_backup_codes))
            .route("/status", web::get().to(get_mfa_status))
            .route("/disable", web::delete().to(disable_mfa))
            .route("/trusted-devices", web::get().to(get_trusted_devices))
            .route("/trusted-devices/{device_id}", web::delete().to(revoke_trusted_device))
            .route("/device-trust-status", web::get().to(check_device_trust))
    );
}
