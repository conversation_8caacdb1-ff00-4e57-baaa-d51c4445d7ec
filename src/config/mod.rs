use serde::Deserialize;


pub mod multi_app;
pub use multi_app::*;

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct Config {
    pub host: String,
    pub port: u16,
    pub database_url: String,
    pub redis_url: String,
    pub jwt_secret: String,
    pub log_level: String,
    pub environment: String,
    pub oauth: OAuthConfig,
    pub email: EmailConfig,

    // Multi-app configuration
    pub service_mode: Option<String>,
    pub default_tenant_id: Option<String>,
    pub multi_app: Option<MultiAppConfig>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct OAuthConfig {
    pub enabled: bool,
    pub base_url: String,
    pub google: OAuthProviderConfig,
    pub github: OAuthProviderConfig,
    pub microsoft: OAuthProviderConfig,
    pub apple: AppleOAuthConfig,
}

#[derive(Debug, Clone, Deserialize)]
pub struct OAuthProviderConfig {
    pub client_id: String,
    pub client_secret: String,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct AppleOAuthConfig {
    pub client_id: String,
    pub private_key_path: String,
    pub key_id: String,
    pub team_id: String,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct EmailConfig {
    pub provider: String,
    pub resend_api_key: String,
    pub from_address: String,
    pub from_name: String,
    pub reply_to: String,
    pub smtp: SmtpConfig,
}

#[derive(Debug, Clone, Deserialize)]
pub struct SmtpConfig {
    pub host: String,
    pub port: u16,
    pub user: String,
    pub password: String,
}

impl Config {
    pub fn from_env() -> Result<Self, config::ConfigError> {
        dotenvy::dotenv().ok();

        let mut cfg = config::Config::builder()
            .set_default("host", "0.0.0.0")?
            .set_default("port", 8080)?
            .set_default("log_level", "info")?
            .set_default("environment", "development")?
            // Multi-app defaults
            .set_default("service_mode", "integrated")?
            .set_default("default_tenant_id", "crabshield-main")?
            // OAuth defaults
            .set_default("oauth.enabled", false)?
            .set_default("oauth.base_url", "http://localhost:8001")?
            .set_default("oauth.google.client_id", "")?
            .set_default("oauth.google.client_secret", "")?
            .set_default("oauth.github.client_id", "")?
            .set_default("oauth.github.client_secret", "")?
            .set_default("oauth.microsoft.client_id", "")?
            .set_default("oauth.microsoft.client_secret", "")?
            .set_default("oauth.apple.client_id", "")?
            .set_default("oauth.apple.private_key_path", "")?
            .set_default("oauth.apple.key_id", "")?
            .set_default("oauth.apple.team_id", "")?
            // Email defaults
            .set_default("email.provider", "resend")?
            .set_default("email.resend_api_key", "")?
            .set_default("email.from_address", "<EMAIL>")?
            .set_default("email.from_name", "CrabShield")?
            .set_default("email.reply_to", "<EMAIL>")?
            .set_default("email.smtp.host", "smtp.resend.com")?
            .set_default("email.smtp.port", 465)?
            .set_default("email.smtp.user", "resend")?
            .set_default("email.smtp.password", "")?;

        // Add environment variables with custom mapping
        cfg = cfg.add_source(
            config::Environment::default()
                .separator("_")
                .with_list_parse_key("oauth_providers")
                .list_separator(",")
        );

        let config: Config = cfg.build()?.try_deserialize()?;

        // Validate JWT secret strength
        Self::validate_jwt_secret(&config.jwt_secret)?;

        Ok(config)
    }

    /// Validate JWT secret strength for security
    fn validate_jwt_secret(secret: &str) -> Result<(), config::ConfigError> {
        if secret.len() < 32 {
            return Err(config::ConfigError::Message(
                "JWT secret must be at least 32 characters long for security".to_string()
            ));
        }

        // Check for common weak secrets
        let weak_secrets = [
            "secret", "password", "123456", "jwt_secret", "your_secret_here",
            "change_me", "default", "test", "development", "production"
        ];

        let secret_lower = secret.to_lowercase();
        for weak in &weak_secrets {
            if secret_lower.contains(weak) {
                return Err(config::ConfigError::Message(
                    format!("JWT secret appears to contain weak pattern: '{}'. Use a strong, random secret.", weak)
                ));
            }
        }

        // Check for sufficient entropy (basic check)
        let unique_chars: std::collections::HashSet<char> = secret.chars().collect();
        if unique_chars.len() < 10 {
            return Err(config::ConfigError::Message(
                "JWT secret has insufficient character diversity. Use a more complex secret.".to_string()
            ));
        }

        Ok(())
    }

    pub fn is_standalone_mode(&self) -> bool {
        self.service_mode.as_deref() == Some("standalone")
    }

    pub fn load_multi_app_config() -> Result<MultiAppConfig, config::ConfigError> {
        dotenvy::dotenv().ok();

        // Load multi-app configuration from environment variables
        // This is a basic implementation - in production you might want to load from files
        let cfg = config::Config::builder()
            .add_source(config::Environment::default().separator("_"))
            .build()?;

        cfg.try_deserialize()
    }
}
